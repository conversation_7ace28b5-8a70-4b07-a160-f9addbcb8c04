import 'package:arepsalin/data_layer/provider/user_provider.dart';
import 'package:arepsalin/managers/secure_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:arepsalin/data_layer/provider/authentication_provider.dart';
import 'package:arepsalin/data_layer/provider/language_provider.dart';
import 'package:arepsalin/managers/shared_pref_manager.dart';
import 'package:arepsalin/presentation_layer/auth_feature/screens/login_screen.dart';
import 'package:arepsalin/translation/locale_keys.g.dart';

import '../../../constants/app_colors.dart';
import '../../../managers/theme_manager.dart';
import '../../widgets/custom_image.dart';
import '../../widgets/custom_progress_indicator.dart';
import 'grades_screen.dart';

class UserScreen extends StatefulWidget {
  const UserScreen({Key? key}) : super(key: key);

  @override
  State<UserScreen> createState() => _UserScreenState();
}

class _UserScreenState extends State<UserScreen> {
  late LanguageProvider langProvider;
  late UserProvider userProvider;

  String getFirstTwoNames(String fullName) {
    List<String> names = fullName.split(" ");

    // Check if there are at least two names
    if (names.length >= 2) {
      String firstName = names[0];
      String secondName = names[1];

      return "$firstName $secondName";
    } else {
      return fullName.isNotEmpty ? fullName : "مستخدم";
    }
  }

  String formatDate(DateTime date) {
    return "${date.day}/${date.month}/${date.year}";
  }

  String? getProfileImageUrl() {
    if (userProvider.student?.user.profilePicture != null &&
        userProvider.student!.user.profilePicture.isNotEmpty) {
      return userProvider.student!.user.profilePicture;
    }
    return null; // Return null to show default avatar
  }

  @override
  void initState() {
    super.initState();
    langProvider = Provider.of(context, listen: false);
    userProvider = Provider.of<UserProvider>(context, listen: false);

    // Fetch user data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      userProvider.fetchUser();
    });
  }

  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.sizeOf(context);
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.light,
        ),
        centerTitle: true,
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.primary,
        title: Text(
          LocaleKeys.userScreen.tr(),
          style: ThemeManager.semiBold(size: 22, color: AppColors.white),
        ),
        titleTextStyle:
            ThemeManager.semiBold(size: 20, color: AppColors.warmWhite),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primary,
              AppColors.primary.withOpacity(0.8),
              AppColors.warmWhite,
            ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: Stack(
          children: [
            // Background decoration
            Positioned(
              top: -50,
              right: -50,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ),
            Positioned(
              top: 100,
              left: -30,
              child: Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.05),
                ),
              ),
            ),
          Container(
            width: size.width,
            padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: size.width * 0.9,
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        Colors.white.withOpacity(0.95),
                        Colors.white.withOpacity(0.9),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        spreadRadius: 0,
                        offset: const Offset(0, 10),
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 40,
                        spreadRadius: 0,
                        offset: const Offset(0, 20),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.primary.withOpacity(0.1),
                              AppColors.primary.withOpacity(0.05),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withOpacity(0.2),
                              blurRadius: 20,
                              spreadRadius: 0,
                              offset: const Offset(0, 8),
                            ),
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 40,
                              spreadRadius: 0,
                              offset: const Offset(0, 16),
                            ),
                          ],
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 3,
                          ),
                        ),
                        padding: const EdgeInsets.all(4),
                        clipBehavior: Clip.hardEdge,
                        child: Consumer<UserProvider>(
                          builder: (context, userProvider, child) {
                            if (userProvider.isLoading) {
                              return SizedBox(
                                height: size.height / 5.2,
                                width: size.height / 5.2,
                                child: const CustomProgressIndicator(),
                              );
                            }

                            final profileImageUrl = getProfileImageUrl();

                            if (profileImageUrl != null && profileImageUrl.isNotEmpty) {
                              // Show user's actual profile picture
                              return CustomImage(
                                addRadius: true,
                                image: profileImageUrl,
                                fromAssets: false,
                                radius: 100,
                                height: size.height / 5.2,
                                width: size.height / 5.2,
                              );
                            } else {
                              // Show default avatar when no profile picture
                              return Container(
                                height: size.height / 5.2,
                                width: size.height / 5.2,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: AppColors.primary.withOpacity(0.1),
                                  border: Border.all(
                                    color: AppColors.primary.withOpacity(0.3),
                                    width: 2,
                                  ),
                                ),
                                child: Icon(
                                  Icons.person,
                                  size: size.height / 8,
                                  color: AppColors.primary.withOpacity(0.7),
                                ),
                              );
                            }
                          },
                        ),
                      ),
                      Consumer<UserProvider>(
                        builder: (context, userProvider, child) {
                          if (userProvider.isLoading) {
                            return Text(
                              "جاري التحميل...",
                              style: ThemeManager.semiBold(
                                  size: 18, color: AppColors.black),
                            );
                          }

                          if (userProvider.student == null) {
                            return Text(
                              "مستخدم",
                              style: ThemeManager.semiBold(
                                  size: 18, color: AppColors.black),
                            );
                          }

                          return Text(
                            getFirstTwoNames(userProvider.student!.user.name),
                            style: ThemeManager.semiBold(
                                size: 20, color: AppColors.black),
                          );
                        },
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Column(
                          children: [
                            const SizedBox(
                              height: 30,
                            ),
                            _buildActionCard(
                              icon: Icons.grade,
                              title: LocaleKeys.grades.tr(),
                              onTap: () {
                                PersistentNavBarNavigator.pushNewScreen(
                                  context,
                                  screen: const GradesScreen(),
                                  withNavBar: true,
                                  pageTransitionAnimation:
                                      PageTransitionAnimation.cupertino,
                                );
                              },
                            ),
                            const SizedBox(
                              height: 20,
                            ),

                            // User Information Section
                            Consumer<UserProvider>(
                              builder: (context, userProvider, child) {
                                if (userProvider.student == null) {
                                  return const SizedBox.shrink();
                                }

                                final student = userProvider.student!;
                                final user = student.user;

                                return Column(
                                  children: [
                                    // Personal Information Card
                                    _buildInfoCard(
                                      title: "المعلومات الشخصية",
                                      icon: Icons.person,
                                      children: [
                                        _buildInfoRow("الاسم الكامل", user.name),
                                        _buildInfoRow("البريد الإلكتروني", user.email),
                                        _buildInfoRow("تاريخ الميلاد", formatDate(user.birthday)),
                                        _buildInfoRow("الجنسية", user.nationality),
                                        _buildInfoRow("العنوان", user.address),
                                        _buildInfoRow("الجنس", user.gender == 'male' ? 'ذكر' : 'أنثى'),
                                        _buildInfoRow("الصورة الشخصية", user.profilePicture.isNotEmpty ? "متوفرة" : "غير متوفرة"),
                                      ],
                                    ),

                                    const SizedBox(height: 20),

                                    // Student Information Card
                                    _buildInfoCard(
                                      title: "معلومات الطالب",
                                      icon: Icons.school,
                                      children: [
                                        _buildInfoRow("كود الطالب", student.studentCode),
                                        _buildInfoRow("المدينة", student.city),
                                        _buildInfoRow("الكنيسة", student.church),
                                        _buildInfoRow("أب الاعتراف", student.abEle3traf),
                                        _buildInfoRow("مستوى الشماسية", student.deaconLevel),
                                        _buildInfoRow("خدمة الكنيسة", student.churchService),
                                        _buildInfoRow("المؤهلات", student.qualifications),
                                        _buildInfoRow("حالة التحقق", student.isVerified ? "تم التحقق" : "لم يتم التحقق"),
                                      ],
                                    ),

                                    const SizedBox(height: 20),
                                  ],
                                );
                              },
                            ),
                            Container(
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white,
                                    Colors.white.withOpacity(0.95),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.primary.withOpacity(0.1),
                                    blurRadius: 15,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 5),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 30,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 10),
                                  ),
                                ],
                                border: Border.all(
                                  color: AppColors.primary.withOpacity(0.1),
                                  width: 1,
                                ),
                              ),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(20),
                                  onTap: () {
                                    setState(() {
                                      isExpanded = !isExpanded;
                                    });
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Consumer<LanguageProvider>(
                                      builder: (context, value, child) {
                                        langProvider = value;
                                        langProvider.loadLanguage();
                                        return Column(
                                          children: [
                                            Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(8),
                                                  decoration: BoxDecoration(
                                                    gradient: LinearGradient(
                                                      begin: Alignment.topLeft,
                                                      end: Alignment.bottomRight,
                                                      colors: [
                                                        AppColors.primary.withOpacity(0.15),
                                                        AppColors.primary.withOpacity(0.08),
                                                      ],
                                                    ),
                                                    borderRadius: BorderRadius.circular(12),
                                                  ),
                                                  child: const Icon(
                                                    Icons.language_outlined,
                                                    color: AppColors.primary,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Expanded(
                                                  child: Text(
                                                    LocaleKeys.lang.tr(),
                                                    style: ThemeManager.semiBold(
                                                        size: 16,
                                                        color: AppColors.black),
                                                  ),
                                                ),
                                                Icon(
                                                  !isExpanded
                                                      ? Icons.keyboard_arrow_down
                                                      : Icons.keyboard_arrow_up,
                                                  color: AppColors.grey,
                                                  size: 24,
                                                )
                                              ],
                                            ),
                                        !isExpanded
                                            ? Container()
                                            : Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 11.0),
                                                child: Column(
                                                  children: [
                                                    Container(
                                                      width: double.infinity,
                                                      height: 1,
                                                      color: AppColors
                                                          .actionsColor
                                                          .withOpacity(0.5),
                                                    ),
                                                    const SizedBox(
                                                      height: 10,
                                                    ),
                                                    InkWell(
                                                      onTap: () {
                                                        langProvider
                                                            .setLanguage(
                                                                context, 'ar');
                                                      },
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                            'اللغة العربية',
                                                            style: ThemeManager.medium(
                                                                size: 18,
                                                                color: langProvider
                                                                            .currentLanguage ==
                                                                        'ar'
                                                                    ? AppColors
                                                                        .primary
                                                                    : AppColors
                                                                        .grey),
                                                          ),
                                                          const Spacer(),
                                                          langProvider.currentLanguage ==
                                                                  'ar'
                                                              ? const Icon(
                                                                  Icons.check,
                                                                  color: AppColors
                                                                      .primary,
                                                                )
                                                              : Container()
                                                        ],
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 10,
                                                    ),
                                                    InkWell(
                                                      onTap: () {
                                                        langProvider
                                                            .setLanguage(
                                                                context, 'en');
                                                      },
                                                      child: Row(
                                                        children: [
                                                          Text(
                                                            'English',
                                                            style: ThemeManager.medium(
                                                                size: 18,
                                                                color: langProvider
                                                                            .currentLanguage ==
                                                                        'en'
                                                                    ? AppColors
                                                                        .primary
                                                                    : AppColors
                                                                        .grey),
                                                          ),
                                                          const Spacer(),
                                                          langProvider.currentLanguage ==
                                                                  'en'
                                                              ? const Icon(
                                                                  Icons.check,
                                                                  color: AppColors
                                                                      .primary,
                                                                )
                                                              : Container()
                                                        ],
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 0,
                                                    ),
                                                  ],
                                                ),
                                              )
                                      ],
                                    );
                                  },
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 30,
                            ),
                            Container(
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppColors.primary,
                                    AppColors.primary.withOpacity(0.9),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.primary.withOpacity(0.3),
                                    blurRadius: 15,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 8),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 30,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 15),
                                  ),
                                ],
                              ),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(20),
                                  onTap: () {
                                    PersistentNavBarNavigator.pushNewScreen(
                                      context,
                                      screen: const LoginScreen(),
                                      withNavBar: false,
                                      pageTransitionAnimation:
                                          PageTransitionAnimation.cupertino,
                                    );
                                    SecureStorage s = SecureStorage();
                                    s.deleteToken("refresh_token");
                                    s.deleteToken("access_token");
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 24, vertical: 16),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        const Icon(
                                          Icons.logout,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          LocaleKeys.logOut.tr(),
                                          style: ThemeManager.semiBold(
                                            size: 16,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.white.withOpacity(0.98),
            Colors.white.withOpacity(0.95),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.08),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 40,
            spreadRadius: 0,
            offset: const Offset(0, 16),
          ),
        ],
        border: Border.all(
          color: AppColors.primary.withOpacity(0.08),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary.withOpacity(0.15),
                      AppColors.primary.withOpacity(0.08),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.2),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: ThemeManager.semiBold(
                  size: 18,
                  color: AppColors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: ThemeManager.medium(
                size: 14,
                color: AppColors.grey,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 3,
            child: Text(
              value.isNotEmpty ? value : "غير محدد",
              style: ThemeManager.semiBold(
                size: 14,
                color: AppColors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.white.withOpacity(0.95),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.1),
            blurRadius: 15,
            spreadRadius: 0,
            offset: const Offset(0, 5),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 30,
            spreadRadius: 0,
            offset: const Offset(0, 10),
          ),
        ],
        border: Border.all(
          color: AppColors.primary.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primary.withOpacity(0.15),
                        AppColors.primary.withOpacity(0.08),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.primary,
                    size: 22,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: ThemeManager.semiBold(
                      size: 16,
                      color: AppColors.black,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.grey,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
